# Speech-to-Speech Assistant Setup Guide

## 🎯 Features
- Real-time speech recognition from microphone and system audio
- Instant responses from Gemini AI
- Terminal-based output (no speech synthesis)
- Support for Hindi and English
- Voice Activity Detection for better accuracy
- Colored terminal output for better readability

## 📋 Prerequisites

### 1. Python Requirements
- Python 3.7 or higher
- pip package manager

### 2. System Requirements (Windows)
- Stereo Mix enabled (already done by user)
- Microphone access permissions
- Internet connection for Gemini API

## 🚀 Installation Steps

### Step 1: Install Dependencies
```bash
pip install -r requirements.txt
```

### Step 2: Install PyAudio (Windows specific)
If PyAudio installation fails, try:
```bash
pip install pipwin
pipwin install pyaudio
```

Or download the wheel file from:
https://www.lfd.uci.edu/~gohlke/pythonlibs/#pyaudio

### Step 3: Verify Audio Devices
Run the script to see available audio devices:
```bash
python speech_to_speech.py
```

## 🎤 Audio Device Setup

### For System Audio Capture (YouTube, Meetings):
1. Right-click on speaker icon in system tray
2. Select "Open Sound settings"
3. Click "Sound Control Panel"
4. Go to "Recording" tab
5. Enable "Stereo Mix" (already done)
6. Set as default or note the device number

### For Microphone + System Audio:
- Use "Stereo Mix" device to capture all system audio
- Or use virtual audio cable software like VB-Cable

## 🔧 Configuration

### API Key
The Gemini API key is already configured in the script:
```python
API_KEY = "AIzaSyDojJ1_9lDwidsMp_ymykrI9DOxYO39WtE"
```

### Adjustable Parameters
In `speech_to_speech.py`, you can modify:
- `silence_threshold`: How long to wait before processing speech (default: 30 frames)
- `sample_rate`: Audio quality (default: 16000 Hz)
- `vad` aggressiveness: Voice detection sensitivity (0-3, default: 2)

## 🎮 Usage

### Basic Usage
```bash
python speech_to_speech.py
```

### Device Selection
1. Run the script
2. See list of available audio devices
3. Enter device number or press Enter for default
4. Start speaking!

### Controls
- **Ctrl+C**: Stop the assistant
- **Speak clearly**: For better recognition
- **Wait for processing**: Let it finish before speaking again

## 🔍 Troubleshooting

### Common Issues

#### 1. PyAudio Installation Error
```bash
# Try alternative installation
pip install --upgrade pip
pip install pyaudio --force-reinstall
```

#### 2. No Audio Devices Found
- Check microphone permissions
- Restart the script
- Try running as administrator

#### 3. Speech Recognition Errors
- Speak clearly and loudly
- Reduce background noise
- Check internet connection
- Adjust microphone levels in Windows

#### 4. Gemini API Errors
- Verify API key is correct
- Check internet connection
- Ensure API quota is not exceeded

#### 5. System Audio Not Captured
- Ensure Stereo Mix is enabled and set as default
- Try different audio device indices
- Check Windows audio settings

### Performance Tips
1. **Reduce Latency**: Lower `silence_threshold` value
2. **Better Accuracy**: Increase `vad` aggressiveness
3. **System Audio**: Use Stereo Mix device
4. **Meeting Audio**: Position microphone closer to speakers

## 📊 Expected Performance
- **Response Time**: 1-3 seconds (depending on internet speed)
- **Accuracy**: 85-95% for clear speech
- **Languages**: Hindi and English supported
- **Audio Sources**: Microphone, System audio, Meeting audio

## 🎯 Use Cases
- ✅ YouTube video Q&A
- ✅ Online meeting assistance
- ✅ Real-time translation help
- ✅ Voice-based research
- ✅ Accessibility assistance

## 🔒 Security Notes
- API key is embedded in script (consider using environment variables for production)
- Audio is processed locally and sent to Google/Gemini APIs
- No audio is stored permanently

## 📞 Support
If you encounter issues:
1. Check the troubleshooting section
2. Verify all dependencies are installed
3. Test with different audio devices
4. Check Windows audio settings
