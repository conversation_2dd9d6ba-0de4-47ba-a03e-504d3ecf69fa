# Smart Speech-to-Speech Assistant Setup Guide

## 🎯 Enhanced Features
- **Auto Device Detection** - Automatically selects the best available audio device
- **Dynamic Device Switching** - Auto-switches when Bluetooth devices connect/disconnect
- **Real-time Device Monitoring** - Monitors device changes in background
- **Smart Fallback** - Automatically switches to working devices if current fails
- **System Audio Capture** - Captures YouTube, meetings, and system audio
- **Instant Gemini Responses** - 1-2 second response time in terminal
- **Multi-language Support** - Hindi and English recognition
- **Voice Activity Detection** - Processes only when speech is detected
- **Colored Terminal Output** - Easy-to-read colored responses

## 📋 Prerequisites

### 1. Python Requirements
- Python 3.7 or higher
- pip package manager

### 2. System Requirements (Windows)
- Stereo Mix enabled (already done by user)
- Microphone access permissions
- Internet connection for Gemini API

## 🚀 Installation Steps

### Step 1: Install Dependencies
```bash
pip install -r requirements.txt
```

### Step 2: Install PyAudio (Windows specific)
If PyAudio installation fails, try:
```bash
pip install pipwin
pipwin install pyaudio
```

Or download the wheel file from:
https://www.lfd.uci.edu/~gohlke/pythonlibs/#pyaudio

### Step 3: Verify Audio Devices
Run the script to see available audio devices:
```bash
python speech_to_speech.py
```

## 🎤 Audio Device Setup

### For System Audio Capture (YouTube, Meetings):
1. Right-click on speaker icon in system tray
2. Select "Open Sound settings"
3. Click "Sound Control Panel"
4. Go to "Recording" tab
5. Enable "Stereo Mix" (already done)
6. Set as default or note the device number

### For Microphone + System Audio:
- Use "Stereo Mix" device to capture all system audio
- Or use virtual audio cable software like VB-Cable

## 🔧 Configuration

### API Key
The Gemini API key is already configured in the script:
```python
API_KEY = "AIzaSyDojJ1_9lDwidsMp_ymykrI9DOxYO39WtE"
```

### Adjustable Parameters
In `speech_to_speech.py`, you can modify:
- `silence_threshold`: How long to wait before processing speech (default: 30 frames)
- `sample_rate`: Audio quality (default: 16000 Hz)
- `vad` aggressiveness: Voice detection sensitivity (0-3, default: 2)

## 🎮 Usage

### Quick Start (Recommended)
```bash
python speech_to_speech.py
```
- Choose option 1 for auto device selection
- Script will automatically find and use the best device
- No manual configuration needed!

### Advanced Usage
```bash
# Test your audio devices first
python device_test.py

# Then run the main script
python speech_to_speech.py
```

### Device Selection Options
1. **Auto Selection (Recommended)**: Script automatically picks the best device
2. **Manual Selection**: Choose specific device from the list

### Smart Features in Action
- **Bluetooth Connect**: Script auto-switches to Bluetooth when connected
- **Bluetooth Disconnect**: Script auto-switches back to default device
- **Device Failure**: Script automatically finds and switches to working device
- **System Audio**: Captures YouTube, meetings, and all system sounds

### Controls
- **Ctrl+C**: Stop the assistant
- **Speak clearly**: For better recognition
- **No restart needed**: Device changes are handled automatically

## 🔍 Troubleshooting

### Common Issues

#### 1. PyAudio Installation Error
```bash
# Try alternative installation
pip install --upgrade pip
pip install pyaudio --force-reinstall
```

#### 2. No Audio Devices Found
- Check microphone permissions
- Restart the script
- Try running as administrator

#### 3. Speech Recognition Errors
- Speak clearly and loudly
- Reduce background noise
- Check internet connection
- Adjust microphone levels in Windows

#### 4. Gemini API Errors
- Verify API key is correct
- Check internet connection
- Ensure API quota is not exceeded

#### 5. System Audio Not Captured
- Ensure Stereo Mix is enabled and set as default
- Try different audio device indices
- Check Windows audio settings

### Performance Tips
1. **Reduce Latency**: Lower `silence_threshold` value
2. **Better Accuracy**: Increase `vad` aggressiveness
3. **System Audio**: Use Stereo Mix device
4. **Meeting Audio**: Position microphone closer to speakers

## 📊 Expected Performance
- **Response Time**: 1-3 seconds (depending on internet speed)
- **Accuracy**: 85-95% for clear speech
- **Languages**: Hindi and English supported
- **Audio Sources**: Microphone, System audio, Meeting audio

## 🎯 Use Cases
- ✅ YouTube video Q&A
- ✅ Online meeting assistance
- ✅ Real-time translation help
- ✅ Voice-based research
- ✅ Accessibility assistance

## 🔒 Security Notes
- API key is embedded in script (consider using environment variables for production)
- Audio is processed locally and sent to Google/Gemini APIs
- No audio is stored permanently

## 📞 Support
If you encounter issues:
1. Check the troubleshooting section
2. Verify all dependencies are installed
3. Test with different audio devices
4. Check Windows audio settings
