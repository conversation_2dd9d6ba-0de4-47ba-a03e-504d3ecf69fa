#!/usr/bin/env python3
"""
Real-time Speech-to-Speech Assistant using Gemini API
Captures audio from microphone and system audio, converts to text,
gets response from <PERSON>, and prints in terminal.
"""

import pyaudio
import speech_recognition as sr
import google.generativeai as genai
import threading
import queue
import time
import sys
import os
from colorama import init, Fore, Style
import numpy as np
import webrtcvad

# Initialize colorama for colored terminal output
init()

class SpeechToSpeechAssistant:
    def __init__(self, api_key):
        # Configuration
        self.api_key = api_key
        self.chunk_size = 1024
        self.sample_rate = 16000
        self.channels = 1
        self.format = pyaudio.paInt16
        
        # Audio processing
        self.audio = pyaudio.PyAudio()
        self.recognizer = sr.Recognizer()
        self.microphone = None
        self.audio_queue = queue.Queue()
        self.is_listening = False
        
        # Voice Activity Detection
        self.vad = webrtcvad.Vad(2)  # Aggressiveness level 0-3
        self.silence_threshold = 30  # frames of silence before processing
        self.speech_frames = []
        self.silence_count = 0
        
        # Gemini AI setup
        genai.configure(api_key=self.api_key)
        self.model = genai.GenerativeModel('gemini-pro')
        
        # Threading
        self.processing_thread = None
        self.listening_thread = None
        
        print(f"{Fore.GREEN}🎤 Speech-to-Speech Assistant Initialized{Style.RESET_ALL}")
        
    def list_audio_devices(self):
        """List all available audio input devices"""
        print(f"\n{Fore.CYAN}Available Audio Devices:{Style.RESET_ALL}")
        device_count = self.audio.get_device_count()
        
        for i in range(device_count):
            device_info = self.audio.get_device_info_by_index(i)
            if device_info['maxInputChannels'] > 0:
                print(f"{i}: {device_info['name']} - {device_info['maxInputChannels']} channels")
        
        return device_count
    
    def setup_microphone(self, device_index=None):
        """Setup microphone for audio capture"""
        try:
            if device_index is None:
                # Auto-detect default microphone
                self.microphone = sr.Microphone(sample_rate=self.sample_rate)
            else:
                self.microphone = sr.Microphone(device_index=device_index, sample_rate=self.sample_rate)
            
            # Adjust for ambient noise
            print(f"{Fore.YELLOW}🔧 Adjusting for ambient noise... Please wait{Style.RESET_ALL}")
            with self.microphone as source:
                self.recognizer.adjust_for_ambient_noise(source, duration=2)
            
            print(f"{Fore.GREEN}✅ Microphone setup complete{Style.RESET_ALL}")
            return True
            
        except Exception as e:
            print(f"{Fore.RED}❌ Error setting up microphone: {e}{Style.RESET_ALL}")
            return False
    
    def is_speech(self, audio_chunk):
        """Check if audio chunk contains speech using VAD"""
        try:
            # Convert audio to the format expected by webrtcvad
            audio_data = np.frombuffer(audio_chunk, dtype=np.int16)
            
            # webrtcvad expects 10, 20, or 30ms frames at 8000, 16000, or 32000 Hz
            frame_duration = 30  # ms
            frame_size = int(self.sample_rate * frame_duration / 1000)
            
            if len(audio_data) >= frame_size:
                frame = audio_data[:frame_size].tobytes()
                return self.vad.is_speech(frame, self.sample_rate)
            
            return False
        except:
            return False
    
    def audio_callback(self, in_data, frame_count, time_info, status):
        """Callback function for audio stream"""
        if self.is_listening:
            self.audio_queue.put(in_data)
        return (in_data, pyaudio.paContinue)
    
    def process_audio_stream(self):
        """Process audio stream in real-time"""
        print(f"{Fore.GREEN}🎧 Listening for speech... (Press Ctrl+C to stop){Style.RESET_ALL}")
        
        while self.is_listening:
            try:
                # Get audio data from queue
                if not self.audio_queue.empty():
                    audio_data = self.audio_queue.get()
                    
                    # Check for speech
                    if self.is_speech(audio_data):
                        self.speech_frames.append(audio_data)
                        self.silence_count = 0
                        print(f"{Fore.BLUE}🗣️  Speech detected...{Style.RESET_ALL}", end='\r')
                    else:
                        if self.speech_frames:
                            self.silence_count += 1
                            
                            # If we have enough silence, process the speech
                            if self.silence_count >= self.silence_threshold:
                                self.process_speech()
                                self.speech_frames = []
                                self.silence_count = 0
                
                time.sleep(0.01)  # Small delay to prevent high CPU usage
                
            except Exception as e:
                print(f"\n{Fore.RED}❌ Error in audio processing: {e}{Style.RESET_ALL}")
    
    def process_speech(self):
        """Process collected speech frames"""
        if not self.speech_frames:
            return
        
        try:
            # Combine all speech frames
            audio_data = b''.join(self.speech_frames)
            
            # Convert to AudioData object for speech recognition
            audio_data_sr = sr.AudioData(audio_data, self.sample_rate, 2)
            
            print(f"\n{Fore.YELLOW}🔄 Processing speech...{Style.RESET_ALL}")
            
            # Convert speech to text
            try:
                text = self.recognizer.recognize_google(audio_data_sr, language='hi-IN')
                print(f"{Fore.CYAN}👤 You said: {text}{Style.RESET_ALL}")
                
                # Get response from Gemini
                self.get_gemini_response(text)
                
            except sr.UnknownValueError:
                print(f"{Fore.RED}❌ Could not understand audio{Style.RESET_ALL}")
            except sr.RequestError as e:
                print(f"{Fore.RED}❌ Error with speech recognition: {e}{Style.RESET_ALL}")
                
        except Exception as e:
            print(f"{Fore.RED}❌ Error processing speech: {e}{Style.RESET_ALL}")
    
    def get_gemini_response(self, text):
        """Get response from Gemini API"""
        try:
            print(f"{Fore.YELLOW}🤖 Getting response from Gemini...{Style.RESET_ALL}")
            
            # Generate response
            response = self.model.generate_content(text)
            
            # Print response in terminal
            print(f"\n{Fore.GREEN}🤖 Gemini Response:{Style.RESET_ALL}")
            print(f"{Fore.WHITE}{response.text}{Style.RESET_ALL}")
            print(f"{'-' * 50}")
            
        except Exception as e:
            print(f"{Fore.RED}❌ Error getting Gemini response: {e}{Style.RESET_ALL}")
    
    def start_listening(self, device_index=None):
        """Start the speech recognition process"""
        if not self.setup_microphone(device_index):
            return False
        
        try:
            # Start audio stream
            stream = self.audio.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                input_device_index=device_index,
                frames_per_buffer=self.chunk_size,
                stream_callback=self.audio_callback
            )
            
            self.is_listening = True
            
            # Start processing thread
            self.processing_thread = threading.Thread(target=self.process_audio_stream)
            self.processing_thread.daemon = True
            self.processing_thread.start()
            
            # Start audio stream
            stream.start_stream()
            
            # Keep the main thread alive
            try:
                while self.is_listening:
                    time.sleep(0.1)
            except KeyboardInterrupt:
                print(f"\n{Fore.YELLOW}🛑 Stopping speech recognition...{Style.RESET_ALL}")
                self.stop_listening()
            
            # Clean up
            stream.stop_stream()
            stream.close()
            
        except Exception as e:
            print(f"{Fore.RED}❌ Error starting audio stream: {e}{Style.RESET_ALL}")
            return False
        
        return True
    
    def stop_listening(self):
        """Stop the speech recognition process"""
        self.is_listening = False
        if self.processing_thread:
            self.processing_thread.join(timeout=1)
    
    def __del__(self):
        """Cleanup when object is destroyed"""
        self.stop_listening()
        if hasattr(self, 'audio'):
            self.audio.terminate()

def main():
    # API Key
    API_KEY = "AIzaSyDojJ1_9lDwidsMp_ymykrI9DOxYO39WtE"
    
    print(f"{Fore.MAGENTA}{'=' * 60}{Style.RESET_ALL}")
    print(f"{Fore.MAGENTA}🎤 Real-time Speech-to-Speech Assistant with Gemini AI{Style.RESET_ALL}")
    print(f"{Fore.MAGENTA}{'=' * 60}{Style.RESET_ALL}")
    
    # Create assistant
    assistant = SpeechToSpeechAssistant(API_KEY)
    
    # List available devices
    device_count = assistant.list_audio_devices()
    
    # Ask user to select device
    print(f"\n{Fore.CYAN}Select audio input device:{Style.RESET_ALL}")
    print("Enter device number (or press Enter for default): ", end="")
    
    try:
        user_input = input().strip()
        device_index = None if user_input == "" else int(user_input)
        
        if device_index is not None and (device_index < 0 or device_index >= device_count):
            print(f"{Fore.RED}❌ Invalid device index{Style.RESET_ALL}")
            return
        
    except ValueError:
        print(f"{Fore.RED}❌ Invalid input{Style.RESET_ALL}")
        return
    
    # Start listening
    assistant.start_listening(device_index)

if __name__ == "__main__":
    main()
