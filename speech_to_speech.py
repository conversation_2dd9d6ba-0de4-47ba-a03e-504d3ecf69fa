#!/usr/bin/env python3
"""
Real-time Speech-to-Speech Assistant using Gemini API
Captures audio from microphone and system audio, converts to text,
gets response from <PERSON>, and prints in terminal.
Features: Auto device detection, dynamic switching, real-time monitoring
"""

import pyaudio
import speech_recognition as sr
import google.generativeai as genai
import threading
import queue
import time
import sys
import os
from colorama import init, Fore, Style
import numpy as np
import webrtcvad
import re

# Initialize colorama for colored terminal output
init()

class SpeechToSpeechAssistant:
    def __init__(self, api_key):
        # Configuration
        self.api_key = api_key
        self.chunk_size = 1024
        self.sample_rate = 16000
        self.channels = 1
        self.format = pyaudio.paInt16

        # Audio processing
        self.audio = pyaudio.PyAudio()
        self.recognizer = sr.Recognizer()
        self.microphone = None
        self.audio_queue = queue.Queue()
        self.is_listening = False
        self.current_stream = None

        # Device management
        self.current_device_index = None
        self.current_device_name = ""
        self.device_monitor_thread = None
        self.device_check_interval = 3  # seconds
        self.last_device_check = time.time()

        # Voice Activity Detection
        self.vad = webrtcvad.Vad(2)  # Aggressiveness level 0-3
        self.silence_threshold = 30  # frames of silence before processing
        self.speech_frames = []
        self.silence_count = 0

        # Gemini AI setup
        genai.configure(api_key=self.api_key)
        self.model = genai.GenerativeModel('gemini-pro')

        # Threading
        self.processing_thread = None
        self.listening_thread = None

        print(f"{Fore.GREEN}🎤 Smart Speech-to-Speech Assistant Initialized{Style.RESET_ALL}")
        print(f"{Fore.CYAN}✨ Auto device detection enabled{Style.RESET_ALL}")
        
    def get_available_devices(self):
        """Get list of available audio input devices"""
        devices = []
        device_count = self.audio.get_device_count()

        for i in range(device_count):
            try:
                device_info = self.audio.get_device_info_by_index(i)
                if device_info['maxInputChannels'] > 0:
                    devices.append({
                        'index': i,
                        'name': device_info['name'],
                        'channels': device_info['maxInputChannels'],
                        'sample_rate': device_info['defaultSampleRate']
                    })
            except:
                continue

        return devices

    def find_best_device(self):
        """Automatically find the best available audio device"""
        devices = self.get_available_devices()

        if not devices:
            return None, "No audio devices found"

        # Priority order for device selection
        priority_keywords = [
            'stereo mix',  # For system audio
            'bluetooth',   # Bluetooth devices
            'headset',     # Headsets
            'microphone',  # Regular microphones
            'mic',         # Short for microphone
            'default'      # Default devices
        ]

        # Score devices based on priority
        scored_devices = []
        for device in devices:
            score = 0
            name_lower = device['name'].lower()

            for i, keyword in enumerate(priority_keywords):
                if keyword in name_lower:
                    score = len(priority_keywords) - i
                    break

            # Bonus for higher channel count
            if device['channels'] > 1:
                score += 1

            scored_devices.append((score, device))

        # Sort by score (highest first)
        scored_devices.sort(key=lambda x: x[0], reverse=True)

        if scored_devices:
            best_device = scored_devices[0][1]
            return best_device['index'], best_device['name']

        # Fallback to first available device
        return devices[0]['index'], devices[0]['name']

    def list_audio_devices(self):
        """List all available audio input devices with smart detection"""
        devices = self.get_available_devices()

        if not devices:
            print(f"{Fore.RED}❌ No audio input devices found{Style.RESET_ALL}")
            return 0

        print(f"\n{Fore.CYAN}📱 Available Audio Devices:{Style.RESET_ALL}")

        # Find and highlight the best device
        best_index, best_name = self.find_best_device()

        for device in devices:
            marker = f"{Fore.GREEN}⭐ [RECOMMENDED]" if device['index'] == best_index else "  "
            print(f"{marker} {device['index']}: {device['name']} - {device['channels']} channels{Style.RESET_ALL}")

        print(f"\n{Fore.GREEN}🎯 Auto-selected: {best_name}{Style.RESET_ALL}")
        return len(devices)
    
    def test_device(self, device_index):
        """Test if a device is working"""
        try:
            # Try to open a stream briefly to test the device
            test_stream = self.audio.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                input_device_index=device_index,
                frames_per_buffer=self.chunk_size
            )
            test_stream.close()
            return True
        except:
            return False

    def setup_microphone(self, device_index=None):
        """Setup microphone for audio capture with auto-detection"""
        try:
            if device_index is None:
                # Auto-detect best device
                device_index, device_name = self.find_best_device()
                if device_index is None:
                    print(f"{Fore.RED}❌ No suitable audio device found{Style.RESET_ALL}")
                    return False
                print(f"{Fore.GREEN}🎯 Auto-selected: {device_name}{Style.RESET_ALL}")

            # Test the device first
            if not self.test_device(device_index):
                print(f"{Fore.RED}❌ Selected device is not working, trying fallback...{Style.RESET_ALL}")
                # Try to find another working device
                devices = self.get_available_devices()
                for device in devices:
                    if device['index'] != device_index and self.test_device(device['index']):
                        device_index = device['index']
                        print(f"{Fore.YELLOW}🔄 Switched to: {device['name']}{Style.RESET_ALL}")
                        break
                else:
                    print(f"{Fore.RED}❌ No working audio device found{Style.RESET_ALL}")
                    return False

            self.microphone = sr.Microphone(device_index=device_index, sample_rate=self.sample_rate)
            self.current_device_index = device_index

            # Get device name for display
            try:
                device_info = self.audio.get_device_info_by_index(device_index)
                self.current_device_name = device_info['name']
            except:
                self.current_device_name = f"Device {device_index}"

            # Adjust for ambient noise
            print(f"{Fore.YELLOW}🔧 Adjusting for ambient noise... Please wait{Style.RESET_ALL}")
            with self.microphone as source:
                self.recognizer.adjust_for_ambient_noise(source, duration=1)

            print(f"{Fore.GREEN}✅ Microphone setup complete: {self.current_device_name}{Style.RESET_ALL}")
            return True

        except Exception as e:
            print(f"{Fore.RED}❌ Error setting up microphone: {e}{Style.RESET_ALL}")
            return False
    
    def is_speech(self, audio_chunk):
        """Check if audio chunk contains speech using VAD"""
        try:
            # Convert audio to the format expected by webrtcvad
            audio_data = np.frombuffer(audio_chunk, dtype=np.int16)
            
            # webrtcvad expects 10, 20, or 30ms frames at 8000, 16000, or 32000 Hz
            frame_duration = 30  # ms
            frame_size = int(self.sample_rate * frame_duration / 1000)
            
            if len(audio_data) >= frame_size:
                frame = audio_data[:frame_size].tobytes()
                return self.vad.is_speech(frame, self.sample_rate)
            
            return False
        except:
            return False
    
    def monitor_devices(self):
        """Monitor audio devices for changes (Bluetooth disconnect, etc.)"""
        while self.is_listening:
            try:
                time.sleep(self.device_check_interval)

                # Check if current device is still available and working
                if self.current_device_index is not None:
                    if not self.test_device(self.current_device_index):
                        print(f"\n{Fore.YELLOW}⚠️  Device disconnected: {self.current_device_name}{Style.RESET_ALL}")
                        self.switch_to_best_device()

            except Exception as e:
                print(f"\n{Fore.RED}❌ Error monitoring devices: {e}{Style.RESET_ALL}")

    def switch_to_best_device(self):
        """Switch to the best available device"""
        try:
            # Find new best device
            new_device_index, new_device_name = self.find_best_device()

            if new_device_index is None or new_device_index == self.current_device_index:
                return False

            if not self.test_device(new_device_index):
                print(f"{Fore.RED}❌ New device not working: {new_device_name}{Style.RESET_ALL}")
                return False

            print(f"{Fore.GREEN}🔄 Switching to: {new_device_name}{Style.RESET_ALL}")

            # Stop current stream
            if self.current_stream:
                try:
                    self.current_stream.stop_stream()
                    self.current_stream.close()
                except:
                    pass

            # Setup new microphone
            old_listening = self.is_listening
            self.is_listening = False
            time.sleep(0.5)  # Brief pause

            if self.setup_microphone(new_device_index):
                self.is_listening = old_listening
                self.restart_audio_stream()
                print(f"{Fore.GREEN}✅ Successfully switched to: {new_device_name}{Style.RESET_ALL}")
                return True
            else:
                self.is_listening = old_listening
                return False

        except Exception as e:
            print(f"{Fore.RED}❌ Error switching device: {e}{Style.RESET_ALL}")
            return False

    def restart_audio_stream(self):
        """Restart the audio stream with current device"""
        try:
            if self.current_device_index is not None:
                self.current_stream = self.audio.open(
                    format=self.format,
                    channels=self.channels,
                    rate=self.sample_rate,
                    input=True,
                    input_device_index=self.current_device_index,
                    frames_per_buffer=self.chunk_size,
                    stream_callback=self.audio_callback
                )
                self.current_stream.start_stream()
                return True
        except Exception as e:
            print(f"{Fore.RED}❌ Error restarting audio stream: {e}{Style.RESET_ALL}")
            return False

    def audio_callback(self, in_data, frame_count, time_info, status):
        """Callback function for audio stream"""
        if self.is_listening:
            self.audio_queue.put(in_data)
        return (in_data, pyaudio.paContinue)
    
    def process_audio_stream(self):
        """Process audio stream in real-time"""
        print(f"{Fore.GREEN}🎧 Listening for speech... (Press Ctrl+C to stop){Style.RESET_ALL}")
        
        while self.is_listening:
            try:
                # Get audio data from queue
                if not self.audio_queue.empty():
                    audio_data = self.audio_queue.get()
                    
                    # Check for speech
                    if self.is_speech(audio_data):
                        self.speech_frames.append(audio_data)
                        self.silence_count = 0
                        print(f"{Fore.BLUE}🗣️  Speech detected...{Style.RESET_ALL}", end='\r')
                    else:
                        if self.speech_frames:
                            self.silence_count += 1
                            
                            # If we have enough silence, process the speech
                            if self.silence_count >= self.silence_threshold:
                                self.process_speech()
                                self.speech_frames = []
                                self.silence_count = 0
                
                time.sleep(0.01)  # Small delay to prevent high CPU usage
                
            except Exception as e:
                print(f"\n{Fore.RED}❌ Error in audio processing: {e}{Style.RESET_ALL}")
    
    def process_speech(self):
        """Process collected speech frames"""
        if not self.speech_frames:
            return
        
        try:
            # Combine all speech frames
            audio_data = b''.join(self.speech_frames)
            
            # Convert to AudioData object for speech recognition
            audio_data_sr = sr.AudioData(audio_data, self.sample_rate, 2)
            
            print(f"\n{Fore.YELLOW}🔄 Processing speech...{Style.RESET_ALL}")
            
            # Convert speech to text
            try:
                text = self.recognizer.recognize_google(audio_data_sr, language='hi-IN')
                print(f"{Fore.CYAN}👤 You said: {text}{Style.RESET_ALL}")
                
                # Get response from Gemini
                self.get_gemini_response(text)
                
            except sr.UnknownValueError:
                print(f"{Fore.RED}❌ Could not understand audio{Style.RESET_ALL}")
            except sr.RequestError as e:
                print(f"{Fore.RED}❌ Error with speech recognition: {e}{Style.RESET_ALL}")
                
        except Exception as e:
            print(f"{Fore.RED}❌ Error processing speech: {e}{Style.RESET_ALL}")
    
    def get_gemini_response(self, text):
        """Get response from Gemini API"""
        try:
            print(f"{Fore.YELLOW}🤖 Getting response from Gemini...{Style.RESET_ALL}")
            
            # Generate response
            response = self.model.generate_content(text)
            
            # Print response in terminal
            print(f"\n{Fore.GREEN}🤖 Gemini Response:{Style.RESET_ALL}")
            print(f"{Fore.WHITE}{response.text}{Style.RESET_ALL}")
            print(f"{'-' * 50}")
            
        except Exception as e:
            print(f"{Fore.RED}❌ Error getting Gemini response: {e}{Style.RESET_ALL}")
    
    def start_listening(self, device_index=None):
        """Start the speech recognition process with auto device management"""
        if not self.setup_microphone(device_index):
            return False

        try:
            # Start audio stream
            self.current_stream = self.audio.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                input_device_index=self.current_device_index,
                frames_per_buffer=self.chunk_size,
                stream_callback=self.audio_callback
            )

            self.is_listening = True

            # Start processing thread
            self.processing_thread = threading.Thread(target=self.process_audio_stream)
            self.processing_thread.daemon = True
            self.processing_thread.start()

            # Start device monitoring thread
            self.device_monitor_thread = threading.Thread(target=self.monitor_devices)
            self.device_monitor_thread.daemon = True
            self.device_monitor_thread.start()

            # Start audio stream
            self.current_stream.start_stream()

            print(f"{Fore.GREEN}🎧 Listening with device: {self.current_device_name}{Style.RESET_ALL}")
            print(f"{Fore.CYAN}🔄 Auto device switching enabled{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}💡 Bluetooth devices will auto-switch when connected/disconnected{Style.RESET_ALL}")

            # Keep the main thread alive
            try:
                while self.is_listening:
                    time.sleep(0.1)
            except KeyboardInterrupt:
                print(f"\n{Fore.YELLOW}🛑 Stopping speech recognition...{Style.RESET_ALL}")
                self.stop_listening()

            # Clean up
            if self.current_stream:
                try:
                    self.current_stream.stop_stream()
                    self.current_stream.close()
                except:
                    pass

        except Exception as e:
            print(f"{Fore.RED}❌ Error starting audio stream: {e}{Style.RESET_ALL}")
            return False

        return True
    
    def stop_listening(self):
        """Stop the speech recognition process"""
        self.is_listening = False
        if self.processing_thread:
            self.processing_thread.join(timeout=1)
    
    def __del__(self):
        """Cleanup when object is destroyed"""
        self.stop_listening()
        if hasattr(self, 'audio'):
            self.audio.terminate()

def main():
    # API Key
    API_KEY = "AIzaSyDojJ1_9lDwidsMp_ymykrI9DOxYO39WtE"

    print(f"{Fore.MAGENTA}{'=' * 70}{Style.RESET_ALL}")
    print(f"{Fore.MAGENTA}🎤 Smart Speech-to-Speech Assistant with Auto Device Detection{Style.RESET_ALL}")
    print(f"{Fore.MAGENTA}{'=' * 70}{Style.RESET_ALL}")
    print(f"{Fore.GREEN}✨ Features: Auto device selection, Bluetooth switching, Real-time monitoring{Style.RESET_ALL}")

    # Create assistant
    assistant = SpeechToSpeechAssistant(API_KEY)

    # List available devices
    device_count = assistant.list_audio_devices()

    if device_count == 0:
        print(f"{Fore.RED}❌ No audio devices found. Please check your audio setup.{Style.RESET_ALL}")
        return

    # Ask user for preference
    print(f"\n{Fore.CYAN}🎯 Device Selection Options:{Style.RESET_ALL}")
    print(f"{Fore.WHITE}1. Auto-select best device (Recommended){Style.RESET_ALL}")
    print(f"{Fore.WHITE}2. Manual device selection{Style.RESET_ALL}")
    print(f"{Fore.CYAN}Choose option (1 or 2, default=1): {Style.RESET_ALL}", end="")

    try:
        user_choice = input().strip()

        if user_choice == "2":
            # Manual selection
            print(f"{Fore.CYAN}Enter device number: {Style.RESET_ALL}", end="")
            device_input = input().strip()

            if device_input == "":
                device_index = None
            else:
                device_index = int(device_input)
                if device_index < 0 or device_index >= device_count:
                    print(f"{Fore.RED}❌ Invalid device index{Style.RESET_ALL}")
                    return
        else:
            # Auto selection (default)
            device_index = None
            print(f"{Fore.GREEN}🎯 Using auto device selection{Style.RESET_ALL}")

    except ValueError:
        print(f"{Fore.RED}❌ Invalid input, using auto selection{Style.RESET_ALL}")
        device_index = None

    print(f"\n{Fore.YELLOW}🚀 Starting assistant...{Style.RESET_ALL}")

    # Start listening
    assistant.start_listening(device_index)

if __name__ == "__main__":
    main()
