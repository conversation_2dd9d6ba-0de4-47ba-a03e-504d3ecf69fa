#!/usr/bin/env python3
"""
Improved Speech-to-Speech Assistant
Better recognition with manual trigger and optimized settings
"""

import speech_recognition as sr
import google.generativeai as genai
import time
from colorama import init, Fore, Style
import threading

init()

class ImprovedSpeechAssistant:
    def __init__(self, api_key):
        self.api_key = api_key
        
        # Setup recognizer with optimized settings
        self.recognizer = sr.Recognizer()
        self.recognizer.energy_threshold = 150  # Very sensitive
        self.recognizer.dynamic_energy_threshold = True
        self.recognizer.pause_threshold = 1.5   # Wait longer for complete sentences
        self.recognizer.phrase_threshold = 0.2  # Minimum audio length
        self.recognizer.non_speaking_duration = 1.0
        
        # Gemini AI setup
        genai.configure(api_key=self.api_key)
        self.model = genai.GenerativeModel('gemini-pro')
        
        self.microphone = None
        self.is_running = True
        
        print(f"{Fore.GREEN}🎤 Improved Speech Assistant Initialized{Style.RESET_ALL}")
    
    def setup_microphone(self):
        """Setup the best available microphone"""
        try:
            # Try to use the default microphone
            self.microphone = sr.Microphone(sample_rate=16000)
            
            print(f"{Fore.YELLOW}🔧 Calibrating microphone...{Style.RESET_ALL}")
            with self.microphone as source:
                # Longer calibration for better results
                self.recognizer.adjust_for_ambient_noise(source, duration=3)
                print(f"Energy threshold set to: {self.recognizer.energy_threshold}")
            
            print(f"{Fore.GREEN}✅ Microphone ready{Style.RESET_ALL}")
            return True
            
        except Exception as e:
            print(f"{Fore.RED}❌ Microphone setup failed: {e}{Style.RESET_ALL}")
            return False
    
    def listen_for_speech(self):
        """Listen for speech with improved detection"""
        try:
            print(f"{Fore.CYAN}🎧 Listening... (speak now){Style.RESET_ALL}")
            
            with self.microphone as source:
                # Listen with timeout and phrase limit
                audio = self.recognizer.listen(
                    source, 
                    timeout=10,      # Wait up to 10 seconds for speech
                    phrase_time_limit=8  # Record up to 8 seconds
                )
            
            return audio
            
        except sr.WaitTimeoutError:
            print(f"{Fore.YELLOW}⏰ No speech detected{Style.RESET_ALL}")
            return None
        except Exception as e:
            print(f"{Fore.RED}❌ Error listening: {e}{Style.RESET_ALL}")
            return None
    
    def recognize_speech(self, audio):
        """Recognize speech with multiple language attempts"""
        if not audio:
            return None
        
        print(f"{Fore.YELLOW}🔄 Processing speech...{Style.RESET_ALL}")
        
        # Try different recognition approaches
        recognition_methods = [
            ('hi-IN', 'Hindi'),
            ('en-US', 'English'),
            ('en-IN', 'Indian English'),
            (None, 'Auto-detect')
        ]
        
        for language, lang_name in recognition_methods:
            try:
                if language:
                    text = self.recognizer.recognize_google(audio, language=language)
                else:
                    text = self.recognizer.recognize_google(audio)
                
                print(f"{Fore.GREEN}✅ Recognized ({lang_name}): {text}{Style.RESET_ALL}")
                return text
                
            except sr.UnknownValueError:
                continue
            except sr.RequestError as e:
                print(f"{Fore.RED}❌ Recognition service error: {e}{Style.RESET_ALL}")
                continue
        
        print(f"{Fore.RED}❌ Could not understand speech{Style.RESET_ALL}")
        return None
    
    def get_gemini_response(self, text):
        """Get response from Gemini AI"""
        try:
            print(f"{Fore.YELLOW}🤖 Getting Gemini response...{Style.RESET_ALL}")
            
            # Add context for better responses
            prompt = f"Please provide a helpful and concise response to: {text}"
            response = self.model.generate_content(prompt)
            
            print(f"\n{Fore.GREEN}🤖 Gemini Response:{Style.RESET_ALL}")
            print(f"{Fore.WHITE}{response.text}{Style.RESET_ALL}")
            print(f"{'-' * 60}")
            
            return response.text
            
        except Exception as e:
            print(f"{Fore.RED}❌ Error getting Gemini response: {e}{Style.RESET_ALL}")
            return None
    
    def run_interactive_mode(self):
        """Run in interactive mode with manual triggers"""
        if not self.setup_microphone():
            return
        
        print(f"\n{Fore.MAGENTA}{'=' * 60}{Style.RESET_ALL}")
        print(f"{Fore.MAGENTA}🎤 Interactive Speech Assistant{Style.RESET_ALL}")
        print(f"{Fore.MAGENTA}{'=' * 60}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}💡 Press Enter to start listening, 'q' to quit{Style.RESET_ALL}")
        
        while self.is_running:
            try:
                user_input = input(f"\n{Fore.WHITE}Press Enter to listen (or 'q' to quit): {Style.RESET_ALL}")
                
                if user_input.lower() in ['q', 'quit', 'exit']:
                    break
                
                # Listen for speech
                audio = self.listen_for_speech()
                
                if audio:
                    # Recognize speech
                    text = self.recognize_speech(audio)
                    
                    if text:
                        # Get Gemini response
                        self.get_gemini_response(text)
                    else:
                        print(f"{Fore.YELLOW}💡 Try speaking louder or clearer{Style.RESET_ALL}")
                else:
                    print(f"{Fore.YELLOW}💡 No speech detected, try again{Style.RESET_ALL}")
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"{Fore.RED}❌ Error: {e}{Style.RESET_ALL}")
        
        print(f"\n{Fore.YELLOW}👋 Speech assistant stopped{Style.RESET_ALL}")
    
    def run_continuous_mode(self):
        """Run in continuous listening mode"""
        if not self.setup_microphone():
            return
        
        print(f"\n{Fore.MAGENTA}{'=' * 60}{Style.RESET_ALL}")
        print(f"{Fore.MAGENTA}🎤 Continuous Speech Assistant{Style.RESET_ALL}")
        print(f"{Fore.MAGENTA}{'=' * 60}{Style.RESET_ALL}")
        print(f"{Fore.GREEN}🎧 Listening continuously... (Ctrl+C to stop){Style.RESET_ALL}")
        
        try:
            while self.is_running:
                # Listen for speech
                audio = self.listen_for_speech()
                
                if audio:
                    # Recognize speech
                    text = self.recognize_speech(audio)
                    
                    if text:
                        # Get Gemini response
                        self.get_gemini_response(text)
                
                time.sleep(0.5)  # Brief pause between listening cycles
                
        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}👋 Continuous listening stopped{Style.RESET_ALL}")
        except Exception as e:
            print(f"{Fore.RED}❌ Error in continuous mode: {e}{Style.RESET_ALL}")

def main():
    API_KEY = "AIzaSyDojJ1_9lDwidsMp_ymykrI9DOxYO39WtE"
    
    print(f"{Fore.MAGENTA}🎤 Improved Speech-to-Speech Assistant{Style.RESET_ALL}")
    print(f"{Fore.CYAN}✨ Better recognition, Multiple languages, Optimized settings{Style.RESET_ALL}")
    
    assistant = ImprovedSpeechAssistant(API_KEY)
    
    print(f"\n{Fore.CYAN}Choose mode:{Style.RESET_ALL}")
    print(f"1. Interactive mode (Press Enter to listen)")
    print(f"2. Continuous mode (Always listening)")
    
    try:
        choice = input(f"\n{Fore.WHITE}Enter choice (1 or 2, default=1): {Style.RESET_ALL}").strip()
        
        if choice == "2":
            assistant.run_continuous_mode()
        else:
            assistant.run_interactive_mode()
            
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}👋 Assistant stopped{Style.RESET_ALL}")

if __name__ == "__main__":
    main()
