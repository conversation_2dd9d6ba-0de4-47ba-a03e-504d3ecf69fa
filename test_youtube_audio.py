#!/usr/bin/env python3
"""
YouTube Audio Capture Test
Test if the script can capture and recognize YouTube audio
"""

import pyaudio
import speech_recognition as sr
import numpy as np
import time
from colorama import init, Fore, Style
import threading
import queue

init()

class YouTubeAudioTester:
    def __init__(self):
        self.audio = pyaudio.PyAudio()
        self.recognizer = sr.Recognizer()
        
        # Optimized settings for system audio
        self.recognizer.energy_threshold = 200  # Lower for system audio
        self.recognizer.dynamic_energy_threshold = True
        self.recognizer.pause_threshold = 1.0
        self.recognizer.phrase_threshold = 0.3
        
        self.chunk_size = 1024
        self.sample_rate = 16000
        self.channels = 1
        self.format = pyaudio.paInt16
        
        self.audio_queue = queue.Queue()
        self.is_testing = False
        
    def find_stereo_mix_device(self):
        """Find Stereo Mix or system audio device"""
        devices = []
        device_count = self.audio.get_device_count()
        
        for i in range(device_count):
            try:
                device_info = self.audio.get_device_info_by_index(i)
                if device_info['maxInputChannels'] > 0:
                    name = device_info['name'].lower()
                    if any(keyword in name for keyword in ['stereo mix', 'what u hear', 'wave out mix']):
                        devices.append({
                            'index': i,
                            'name': device_info['name'],
                            'channels': device_info['maxInputChannels']
                        })
            except:
                continue
        
        return devices
    
    def test_device_audio_levels(self, device_index, duration=5):
        """Test audio levels from device"""
        print(f"{Fore.YELLOW}🔊 Testing audio levels for {duration} seconds...{Style.RESET_ALL}")
        print(f"{Fore.CYAN}💡 Play some YouTube audio now!{Style.RESET_ALL}")
        
        try:
            stream = self.audio.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                input_device_index=device_index,
                frames_per_buffer=self.chunk_size
            )
            
            max_amplitude = 0
            frames_with_audio = 0
            total_frames = 0
            
            start_time = time.time()
            while time.time() - start_time < duration:
                try:
                    data = stream.read(self.chunk_size, exception_on_overflow=False)
                    audio_array = np.frombuffer(data, dtype=np.int16)
                    amplitude = np.max(np.abs(audio_array))
                    
                    if amplitude > max_amplitude:
                        max_amplitude = amplitude
                    
                    if amplitude > 500:  # Threshold for detecting audio
                        frames_with_audio += 1
                    
                    total_frames += 1
                    
                    # Visual indicator
                    if amplitude > 1000:
                        print(f"{Fore.GREEN}🔊 Audio detected: {amplitude}{Style.RESET_ALL}", end='\r')
                    elif amplitude > 500:
                        print(f"{Fore.YELLOW}🔉 Low audio: {amplitude}{Style.RESET_ALL}", end='\r')
                    else:
                        print(f"{Fore.RED}🔇 Silence: {amplitude}{Style.RESET_ALL}", end='\r')
                    
                except Exception as e:
                    print(f"\n{Fore.RED}Error reading audio: {e}{Style.RESET_ALL}")
                    break
            
            stream.stop_stream()
            stream.close()
            
            audio_percentage = (frames_with_audio / total_frames) * 100 if total_frames > 0 else 0
            
            print(f"\n\n{Fore.CYAN}📊 Audio Test Results:{Style.RESET_ALL}")
            print(f"  Max Amplitude: {max_amplitude}")
            print(f"  Audio Frames: {frames_with_audio}/{total_frames} ({audio_percentage:.1f}%)")
            
            if audio_percentage > 50:
                print(f"  {Fore.GREEN}✅ Good audio capture!{Style.RESET_ALL}")
                return True
            elif audio_percentage > 10:
                print(f"  {Fore.YELLOW}⚠️  Some audio detected but low{Style.RESET_ALL}")
                return True
            else:
                print(f"  {Fore.RED}❌ No audio detected{Style.RESET_ALL}")
                return False
                
        except Exception as e:
            print(f"\n{Fore.RED}❌ Error testing device: {e}{Style.RESET_ALL}")
            return False
    
    def test_speech_recognition(self, device_index, duration=10):
        """Test speech recognition from device"""
        print(f"\n{Fore.YELLOW}🎤 Testing speech recognition for {duration} seconds...{Style.RESET_ALL}")
        print(f"{Fore.CYAN}💡 Play YouTube video with clear speech!{Style.RESET_ALL}")
        
        try:
            with sr.Microphone(device_index=device_index, sample_rate=self.sample_rate) as source:
                print(f"{Fore.YELLOW}🔧 Adjusting for ambient noise...{Style.RESET_ALL}")
                self.recognizer.adjust_for_ambient_noise(source, duration=1)
                
                print(f"{Fore.GREEN}🎧 Listening for speech...{Style.RESET_ALL}")
                
                start_time = time.time()
                recognition_attempts = 0
                successful_recognitions = 0
                
                while time.time() - start_time < duration:
                    try:
                        # Listen for audio
                        audio = self.recognizer.listen(source, timeout=2, phrase_time_limit=3)
                        recognition_attempts += 1
                        
                        print(f"{Fore.YELLOW}🔄 Attempting recognition #{recognition_attempts}...{Style.RESET_ALL}")
                        
                        # Try recognition
                        try:
                            text = self.recognizer.recognize_google(audio, language='en-US')
                            successful_recognitions += 1
                            print(f"{Fore.GREEN}✅ Recognized: {text}{Style.RESET_ALL}")
                        except sr.UnknownValueError:
                            print(f"{Fore.RED}❌ Could not understand audio{Style.RESET_ALL}")
                        except sr.RequestError as e:
                            print(f"{Fore.RED}❌ Recognition error: {e}{Style.RESET_ALL}")
                            
                    except sr.WaitTimeoutError:
                        print(f"{Fore.BLUE}⏰ No speech detected, continuing...{Style.RESET_ALL}")
                        continue
                
                success_rate = (successful_recognitions / recognition_attempts * 100) if recognition_attempts > 0 else 0
                
                print(f"\n{Fore.CYAN}📊 Recognition Test Results:{Style.RESET_ALL}")
                print(f"  Attempts: {recognition_attempts}")
                print(f"  Successful: {successful_recognitions}")
                print(f"  Success Rate: {success_rate:.1f}%")
                
                if success_rate > 50:
                    print(f"  {Fore.GREEN}✅ Excellent recognition!{Style.RESET_ALL}")
                elif success_rate > 20:
                    print(f"  {Fore.YELLOW}⚠️  Moderate recognition{Style.RESET_ALL}")
                else:
                    print(f"  {Fore.RED}❌ Poor recognition{Style.RESET_ALL}")
                
                return success_rate > 20
                
        except Exception as e:
            print(f"\n{Fore.RED}❌ Error in speech recognition test: {e}{Style.RESET_ALL}")
            return False
    
    def run_full_test(self):
        """Run complete YouTube audio test"""
        print(f"{Fore.MAGENTA}{'=' * 60}{Style.RESET_ALL}")
        print(f"{Fore.MAGENTA}🎵 YouTube Audio Capture Test{Style.RESET_ALL}")
        print(f"{Fore.MAGENTA}{'=' * 60}{Style.RESET_ALL}")
        
        # Find stereo mix devices
        stereo_devices = self.find_stereo_mix_device()
        
        if not stereo_devices:
            print(f"{Fore.RED}❌ No Stereo Mix device found!{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}💡 Enable Stereo Mix in Windows Sound settings{Style.RESET_ALL}")
            return False
        
        print(f"\n{Fore.GREEN}✅ Found Stereo Mix devices:{Style.RESET_ALL}")
        for device in stereo_devices:
            print(f"  [{device['index']}] {device['name']}")
        
        # Test each device
        for device in stereo_devices:
            print(f"\n{Fore.CYAN}🧪 Testing device: {device['name']}{Style.RESET_ALL}")
            
            # Test audio levels
            if self.test_device_audio_levels(device['index']):
                # Test speech recognition
                if self.test_speech_recognition(device['index']):
                    print(f"\n{Fore.GREEN}🎉 Device {device['name']} works perfectly!{Style.RESET_ALL}")
                    return True
                else:
                    print(f"\n{Fore.YELLOW}⚠️  Device captures audio but recognition needs improvement{Style.RESET_ALL}")
            else:
                print(f"\n{Fore.RED}❌ Device {device['name']} not capturing audio{Style.RESET_ALL}")
        
        return False

def main():
    print(f"{Fore.CYAN}🎵 YouTube Audio Capture Tester{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}💡 Make sure YouTube video is playing before starting test{Style.RESET_ALL}")
    
    input(f"\n{Fore.WHITE}Press Enter when YouTube video is playing...{Style.RESET_ALL}")
    
    tester = YouTubeAudioTester()
    
    if tester.run_full_test():
        print(f"\n{Fore.GREEN}🎉 YouTube audio capture is working!{Style.RESET_ALL}")
        print(f"{Fore.CYAN}✨ Your main script should work fine{Style.RESET_ALL}")
    else:
        print(f"\n{Fore.RED}❌ YouTube audio capture needs fixing{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}💡 Check Stereo Mix settings in Windows{Style.RESET_ALL}")

if __name__ == "__main__":
    main()
