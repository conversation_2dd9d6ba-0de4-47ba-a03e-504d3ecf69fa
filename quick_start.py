#!/usr/bin/env python3
"""
Quick Start Script for Speech-to-Speech Assistant
Automatically sets up and runs with best settings
"""

import subprocess
import sys
import os
from colorama import init, Fore, Style

init()

def check_dependencies():
    """Check if all required packages are installed"""
    required_packages = [
        'pyaudio', 'SpeechRecognition', 'google-generativeai', 
        'numpy', 'webrtcvad', 'colorama'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_').lower())
        except ImportError:
            missing_packages.append(package)
    
    return missing_packages

def install_dependencies():
    """Install missing dependencies"""
    print(f"{Fore.YELLOW}📦 Installing dependencies...{Style.RESET_ALL}")
    
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        print(f"{Fore.GREEN}✅ Dependencies installed successfully{Style.RESET_ALL}")
        return True
    except subprocess.CalledProcessError:
        print(f"{Fore.RED}❌ Failed to install dependencies{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}💡 Try running: pip install -r requirements.txt{Style.RESET_ALL}")
        return False

def run_assistant():
    """Run the speech-to-speech assistant"""
    try:
        print(f"\n{Fore.GREEN}🚀 Starting Speech-to-Speech Assistant...{Style.RESET_ALL}")
        subprocess.run([sys.executable, 'speech_to_speech.py'])
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}👋 Assistant stopped by user{Style.RESET_ALL}")
    except Exception as e:
        print(f"{Fore.RED}❌ Error running assistant: {e}{Style.RESET_ALL}")

def main():
    print(f"{Fore.MAGENTA}{'=' * 60}{Style.RESET_ALL}")
    print(f"{Fore.MAGENTA}🎤 Quick Start - Speech-to-Speech Assistant{Style.RESET_ALL}")
    print(f"{Fore.MAGENTA}{'=' * 60}{Style.RESET_ALL}")
    
    # Check if main script exists
    if not os.path.exists('speech_to_speech.py'):
        print(f"{Fore.RED}❌ speech_to_speech.py not found in current directory{Style.RESET_ALL}")
        return
    
    # Check dependencies
    missing = check_dependencies()
    
    if missing:
        print(f"{Fore.YELLOW}⚠️  Missing packages: {', '.join(missing)}{Style.RESET_ALL}")
        
        install_choice = input(f"{Fore.CYAN}Install missing packages? (y/n): {Style.RESET_ALL}").lower()
        
        if install_choice in ['y', 'yes', '']:
            if not install_dependencies():
                return
        else:
            print(f"{Fore.RED}❌ Cannot run without required packages{Style.RESET_ALL}")
            return
    
    print(f"{Fore.GREEN}✅ All dependencies are ready{Style.RESET_ALL}")
    
    # Show quick info
    print(f"\n{Fore.CYAN}🎯 Features Active:{Style.RESET_ALL}")
    print(f"  • Auto device detection enabled")
    print(f"  • Bluetooth auto-switching enabled")
    print(f"  • System audio capture supported")
    print(f"  • Real-time device monitoring active")
    print(f"  • Hindi & English recognition")

    print(f"\n{Fore.GREEN}🚀 Starting assistant automatically in 3 seconds...{Style.RESET_ALL}")

    # Auto start after brief delay
    import time
    for i in range(3, 0, -1):
        print(f"{Fore.YELLOW}{i}...{Style.RESET_ALL}", end=" ", flush=True)
        time.sleep(1)
    print(f"\n{Fore.GREEN}Starting now!{Style.RESET_ALL}")

    # Run the assistant
    run_assistant()

if __name__ == "__main__":
    main()
