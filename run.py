#!/usr/bin/env python3
"""
Super Simple Launcher - Just run and go!
No questions, no options, just automatic everything
"""

import subprocess
import sys
import os
from colorama import init, Fore, Style

init()

def main():
    print(f"{Fore.GREEN}🎤 Starting Speech-to-Speech Assistant...{Style.RESET_ALL}")
    print(f"{Fore.CYAN}✨ Auto device detection, Bluetooth switching, System audio capture{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}🚀 No setup needed - just speak!{Style.RESET_ALL}\n")
    
    try:
        # Run the main script directly
        subprocess.run([sys.executable, 'speech_to_speech.py'])
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}👋 Assistant stopped{Style.RESET_ALL}")
    except FileNotFoundError:
        print(f"{Fore.RED}❌ speech_to_speech.py not found{Style.RESET_ALL}")
    except Exception as e:
        print(f"{Fore.RED}❌ Error: {e}{Style.RESET_ALL}")

if __name__ == "__main__":
    main()
