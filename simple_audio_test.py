#!/usr/bin/env python3
"""
Simple Audio Device Test
Quick test to see available devices and their status
"""

import pyaudio
import speech_recognition as sr
from colorama import init, Fore, Style

init()

def test_audio_devices():
    """Test all available audio devices"""
    audio = pyaudio.PyAudio()
    recognizer = sr.Recognizer()
    
    print(f"{Fore.CYAN}🎵 Testing Audio Devices{Style.RESET_ALL}")
    print(f"{'=' * 50}")
    
    device_count = audio.get_device_count()
    working_devices = []
    
    for i in range(device_count):
        try:
            device_info = audio.get_device_info_by_index(i)
            
            if device_info['maxInputChannels'] > 0:
                name = device_info['name']
                print(f"\n[{i}] {name}")
                print(f"    Channels: {device_info['maxInputChannels']}")
                print(f"    Sample Rate: {device_info['defaultSampleRate']}")
                
                # Test if device works
                try:
                    test_stream = audio.open(
                        format=pyaudio.paInt16,
                        channels=1,
                        rate=16000,
                        input=True,
                        input_device_index=i,
                        frames_per_buffer=1024
                    )
                    test_stream.close()
                    print(f"    Status: {Fore.GREEN}✅ Working{Style.RESET_ALL}")
                    working_devices.append((i, name))
                    
                    # Check if it's a system audio device
                    if any(keyword in name.lower() for keyword in ['stereo mix', 'what u hear', 'wave out']):
                        print(f"    Type: {Fore.CYAN}🎵 System Audio Capture{Style.RESET_ALL}")
                    elif 'bluetooth' in name.lower():
                        print(f"    Type: {Fore.BLUE}📱 Bluetooth Device{Style.RESET_ALL}")
                    elif any(keyword in name.lower() for keyword in ['microphone', 'mic']):
                        print(f"    Type: {Fore.GREEN}🎤 Microphone{Style.RESET_ALL}")
                    
                except Exception as e:
                    print(f"    Status: {Fore.RED}❌ Error: {str(e)[:50]}...{Style.RESET_ALL}")
                    
        except Exception as e:
            continue
    
    audio.terminate()
    
    print(f"\n{Fore.CYAN}📊 Summary:{Style.RESET_ALL}")
    print(f"Total devices: {device_count}")
    print(f"Working input devices: {len(working_devices)}")
    
    if working_devices:
        print(f"\n{Fore.GREEN}✅ Working devices:{Style.RESET_ALL}")
        for index, name in working_devices:
            print(f"  [{index}] {name}")
    else:
        print(f"\n{Fore.RED}❌ No working input devices found{Style.RESET_ALL}")
    
    return working_devices

def test_speech_recognition_quick():
    """Quick speech recognition test"""
    recognizer = sr.Recognizer()
    
    # Optimized settings
    recognizer.energy_threshold = 300
    recognizer.dynamic_energy_threshold = True
    recognizer.pause_threshold = 0.8
    
    print(f"\n{Fore.YELLOW}🎤 Quick Speech Test{Style.RESET_ALL}")
    print(f"Speak something for 3 seconds...")
    
    try:
        with sr.Microphone() as source:
            recognizer.adjust_for_ambient_noise(source, duration=1)
            audio = recognizer.listen(source, timeout=5, phrase_time_limit=3)
            
        print(f"{Fore.YELLOW}🔄 Processing...{Style.RESET_ALL}")
        
        # Try multiple languages
        for lang, lang_name in [('hi-IN', 'Hindi'), ('en-US', 'English')]:
            try:
                text = recognizer.recognize_google(audio, language=lang)
                print(f"{Fore.GREEN}✅ {lang_name}: {text}{Style.RESET_ALL}")
                return True
            except:
                continue
        
        print(f"{Fore.RED}❌ Could not recognize speech{Style.RESET_ALL}")
        return False
        
    except Exception as e:
        print(f"{Fore.RED}❌ Error: {e}{Style.RESET_ALL}")
        return False

def main():
    print(f"{Fore.MAGENTA}🎵 Simple Audio Test for Speech-to-Speech Assistant{Style.RESET_ALL}")
    
    # Test devices
    working_devices = test_audio_devices()
    
    if working_devices:
        # Test speech recognition
        test_speech_recognition_quick()
        
        print(f"\n{Fore.CYAN}💡 Recommendations:{Style.RESET_ALL}")
        
        # Find best device
        for index, name in working_devices:
            if 'stereo mix' in name.lower():
                print(f"  🎵 Use device [{index}] for YouTube audio: {name}")
                break
        else:
            print(f"  🎤 Use device [{working_devices[0][0]}] for microphone: {working_devices[0][1]}")
        
        print(f"\n{Fore.GREEN}🚀 Your speech-to-speech script should work!{Style.RESET_ALL}")
    else:
        print(f"\n{Fore.RED}❌ No working devices found. Check your audio setup.{Style.RESET_ALL}")

if __name__ == "__main__":
    main()
