#!/usr/bin/env python3
"""
Audio Device Testing and Detection Script
Test and monitor audio devices for the Speech-to-Speech Assistant
"""

import pyaudio
import time
from colorama import init, Fore, Style
import threading

init()

class AudioDeviceTester:
    def __init__(self):
        self.audio = pyaudio.PyAudio()
        
    def get_all_devices(self):
        """Get all audio devices (input and output)"""
        devices = []
        device_count = self.audio.get_device_count()
        
        for i in range(device_count):
            try:
                device_info = self.audio.get_device_info_by_index(i)
                devices.append({
                    'index': i,
                    'name': device_info['name'],
                    'max_input_channels': device_info['maxInputChannels'],
                    'max_output_channels': device_info['maxOutputChannels'],
                    'default_sample_rate': device_info['defaultSampleRate'],
                    'host_api': device_info['hostApi']
                })
            except:
                continue
        
        return devices
    
    def test_device_recording(self, device_index, duration=2):
        """Test if device can record audio"""
        try:
            stream = self.audio.open(
                format=pyaudio.paInt16,
                channels=1,
                rate=16000,
                input=True,
                input_device_index=device_index,
                frames_per_buffer=1024
            )
            
            print(f"  {Fore.YELLOW}Testing recording for {duration} seconds...{Style.RESET_ALL}")
            
            # Record for specified duration
            frames = []
            for _ in range(0, int(16000 / 1024 * duration)):
                data = stream.read(1024)
                frames.append(data)
            
            stream.stop_stream()
            stream.close()
            
            return True, "Recording successful"
            
        except Exception as e:
            return False, str(e)
    
    def monitor_device_changes(self, interval=3):
        """Monitor for device changes"""
        print(f"\n{Fore.CYAN}🔍 Monitoring device changes (Ctrl+C to stop)...{Style.RESET_ALL}")
        
        last_devices = set()
        
        try:
            while True:
                current_devices = set()
                devices = self.get_all_devices()
                
                for device in devices:
                    if device['max_input_channels'] > 0:
                        current_devices.add((device['index'], device['name']))
                
                # Check for new devices
                new_devices = current_devices - last_devices
                removed_devices = last_devices - current_devices
                
                if new_devices:
                    for index, name in new_devices:
                        print(f"{Fore.GREEN}➕ Device connected: {name} (Index: {index}){Style.RESET_ALL}")
                
                if removed_devices:
                    for index, name in removed_devices:
                        print(f"{Fore.RED}➖ Device disconnected: {name} (Index: {index}){Style.RESET_ALL}")
                
                last_devices = current_devices
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}Monitoring stopped.{Style.RESET_ALL}")
    
    def display_devices(self):
        """Display all audio devices with detailed info"""
        devices = self.get_all_devices()
        
        print(f"\n{Fore.MAGENTA}{'=' * 80}{Style.RESET_ALL}")
        print(f"{Fore.MAGENTA}🎵 Complete Audio Device Information{Style.RESET_ALL}")
        print(f"{Fore.MAGENTA}{'=' * 80}{Style.RESET_ALL}")
        
        input_devices = [d for d in devices if d['max_input_channels'] > 0]
        output_devices = [d for d in devices if d['max_output_channels'] > 0]
        
        # Input devices
        print(f"\n{Fore.CYAN}🎤 INPUT DEVICES (Microphones):{Style.RESET_ALL}")
        if not input_devices:
            print(f"  {Fore.RED}No input devices found{Style.RESET_ALL}")
        else:
            for device in input_devices:
                status_color = Fore.GREEN
                device_type = self.classify_device(device['name'])
                
                print(f"\n  {status_color}[{device['index']}] {device['name']}{Style.RESET_ALL}")
                print(f"      Type: {device_type}")
                print(f"      Channels: {device['max_input_channels']}")
                print(f"      Sample Rate: {device['default_sample_rate']} Hz")
                
                # Test the device
                success, message = self.test_device_recording(device['index'], 1)
                if success:
                    print(f"      Status: {Fore.GREEN}✅ Working{Style.RESET_ALL}")
                else:
                    print(f"      Status: {Fore.RED}❌ Error: {message}{Style.RESET_ALL}")
        
        # Output devices
        print(f"\n{Fore.YELLOW}🔊 OUTPUT DEVICES (Speakers):{Style.RESET_ALL}")
        if not output_devices:
            print(f"  {Fore.RED}No output devices found{Style.RESET_ALL}")
        else:
            for device in output_devices:
                device_type = self.classify_device(device['name'])
                print(f"  [{device['index']}] {device['name']} - {device_type}")
    
    def classify_device(self, device_name):
        """Classify device type based on name"""
        name_lower = device_name.lower()

        if 'stereo mix' in name_lower:
            return f"{Fore.CYAN}System Audio Capture{Style.RESET_ALL}"
        elif 'bluetooth' in name_lower:
            return f"{Fore.BLUE}Bluetooth Device{Style.RESET_ALL}"
        elif any(word in name_lower for word in ['headset', 'headphone']):
            return f"{Fore.MAGENTA}Headset{Style.RESET_ALL}"
        elif any(word in name_lower for word in ['microphone', 'mic']):
            return f"{Fore.GREEN}Microphone{Style.RESET_ALL}"
        elif 'speaker' in name_lower:
            return f"{Fore.YELLOW}Speaker{Style.RESET_ALL}"
        else:
            return f"{Fore.WHITE}Unknown{Style.RESET_ALL}"
    
    def find_recommended_device(self):
        """Find and recommend the best device"""
        devices = self.get_all_devices()
        input_devices = [d for d in devices if d['max_input_channels'] > 0]
        
        if not input_devices:
            return None
        
        # Priority scoring
        scored_devices = []
        for device in input_devices:
            score = 0
            name_lower = device['name'].lower()
            
            # Priority keywords
            if 'stereo mix' in name_lower:
                score += 10  # Highest priority for system audio
            elif 'bluetooth' in name_lower:
                score += 8
            elif 'headset' in name_lower:
                score += 6
            elif 'microphone' in name_lower:
                score += 4
            
            # Test if device works
            success, _ = self.test_device_recording(device['index'], 0.5)
            if success:
                score += 5
            
            scored_devices.append((score, device))
        
        # Sort by score
        scored_devices.sort(key=lambda x: x[0], reverse=True)
        
        if scored_devices:
            return scored_devices[0][1]
        
        return None
    
    def __del__(self):
        if hasattr(self, 'audio'):
            self.audio.terminate()

def main():
    print(f"{Fore.MAGENTA}🎵 Audio Device Tester for Speech-to-Speech Assistant{Style.RESET_ALL}")
    
    tester = AudioDeviceTester()
    
    while True:
        print(f"\n{Fore.CYAN}Choose an option:{Style.RESET_ALL}")
        print("1. Display all devices")
        print("2. Find recommended device")
        print("3. Monitor device changes")
        print("4. Exit")
        
        choice = input(f"\n{Fore.WHITE}Enter choice (1-4): {Style.RESET_ALL}").strip()
        
        if choice == "1":
            tester.display_devices()
        elif choice == "2":
            recommended = tester.find_recommended_device()
            if recommended:
                print(f"\n{Fore.GREEN}🎯 Recommended Device:{Style.RESET_ALL}")
                print(f"  [{recommended['index']}] {recommended['name']}")
                print(f"  Type: {tester.classify_device(recommended['name'])}")
            else:
                print(f"\n{Fore.RED}❌ No suitable device found{Style.RESET_ALL}")
        elif choice == "3":
            tester.monitor_device_changes()
        elif choice == "4":
            break
        else:
            print(f"{Fore.RED}Invalid choice{Style.RESET_ALL}")

if __name__ == "__main__":
    main()
